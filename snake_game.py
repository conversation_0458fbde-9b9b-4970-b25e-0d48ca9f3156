#!/usr/bin/env python3
"""
Terminal Snake Game
A classic Snake game implementation using Python's curses library.
Controls: Arrow keys to move, 'q' to quit, 'r' to restart after game over.
"""

import curses
import random
import time
from enum import Enum
from collections import deque

class Direction(Enum):
    UP = (-1, 0)
    DOWN = (1, 0)
    LEFT = (0, -1)
    RIGHT = (0, 1)

class SnakeGame:
    def __init__(self, stdscr):
        self.stdscr = stdscr
        self.height, self.width = stdscr.getmaxyx()
        
        # Game boundaries (leave space for borders and UI)
        self.game_height = self.height - 4
        self.game_width = self.width - 2
        self.start_row = 2
        self.start_col = 1
        
        # Initialize curses settings
        curses.curs_set(0)  # Hide cursor
        stdscr.timeout(100)  # Game speed (milliseconds)
        stdscr.keypad(True)  # Enable arrow keys
        
        # Initialize colors if available
        if curses.has_colors():
            curses.start_color()
            curses.init_pair(1, curses.COLOR_GREEN, curses.COLOR_BLACK)  # Snake
            curses.init_pair(2, curses.COLOR_RED, curses.COLOR_BLACK)    # Food
            curses.init_pair(3, curses.COLOR_YELLOW, curses.COLOR_BLACK) # Score
            curses.init_pair(4, curses.COLOR_WHITE, curses.COLOR_RED)    # Game Over
        
        self.reset_game()
    
    def reset_game(self):
        """Reset the game to initial state"""
        # Snake starts in the middle, moving right
        start_row = self.game_height // 2
        start_col = self.game_width // 4
        
        self.snake = deque([
            (start_row, start_col),
            (start_row, start_col - 1),
            (start_row, start_col - 2)
        ])
        
        self.direction = Direction.RIGHT
        self.score = 0
        self.game_over = False
        self.paused = False
        
        # Place first food
        self.place_food()
    
    def place_food(self):
        """Place food at a random location not occupied by snake"""
        while True:
            food_row = random.randint(0, self.game_height - 1)
            food_col = random.randint(0, self.game_width - 1)
            if (food_row, food_col) not in self.snake:
                self.food = (food_row, food_col)
                break
    
    def draw_border(self):
        """Draw game border"""
        # Top and bottom borders
        for col in range(self.width):
            try:
                self.stdscr.addch(1, col, '-')
                if self.start_row + self.game_height < self.height:
                    self.stdscr.addch(self.start_row + self.game_height, col, '-')
            except curses.error:
                pass  # Skip if we can't draw at this position
        
        # Left and right borders
        for row in range(1, min(self.start_row + self.game_height + 1, self.height)):
            try:
                if self.start_col - 1 >= 0:
                    self.stdscr.addch(row, self.start_col - 1, '|')
                if self.start_col + self.game_width < self.width:
                    self.stdscr.addch(row, self.start_col + self.game_width, '|')
            except curses.error:
                pass  # Skip if we can't draw at this position
        
        # Corners
        try:
            if self.start_col - 1 >= 0:
                self.stdscr.addch(1, self.start_col - 1, '+')
                if self.start_row + self.game_height < self.height:
                    self.stdscr.addch(self.start_row + self.game_height, self.start_col - 1, '+')
            
            if self.start_col + self.game_width < self.width:
                self.stdscr.addch(1, self.start_col + self.game_width, '+')
                if self.start_row + self.game_height < self.height:
                    self.stdscr.addch(self.start_row + self.game_height, self.start_col + self.game_width, '+')
        except curses.error:
            pass  # Skip if we can't draw at this position
    
    def draw_snake(self):
        """Draw the snake"""
        for i, (row, col) in enumerate(self.snake):
            screen_row = self.start_row + row
            screen_col = self.start_col + col
            
            if 0 <= screen_row < self.height and 0 <= screen_col < self.width:
                try:
                    if i == 0:  # Head
                        char = 'O'  # Snake head
                    else:  # Body
                        char = '#'  # Snake body
                    
                    if curses.has_colors():
                        self.stdscr.addch(screen_row, screen_col, char, curses.color_pair(1))
                    else:
                        self.stdscr.addch(screen_row, screen_col, char)
                except curses.error:
                    pass  # Skip if we can't draw at this position
    
    def draw_food(self):
        """Draw the food"""
        food_row, food_col = self.food
        screen_row = self.start_row + food_row
        screen_col = self.start_col + food_col
        
        if 0 <= screen_row < self.height and 0 <= screen_col < self.width:
            try:
                char = '*'  # Food character
                if curses.has_colors():
                    self.stdscr.addch(screen_row, screen_col, char, curses.color_pair(2))
                else:
                    self.stdscr.addch(screen_row, screen_col, char)
            except curses.error:
                pass  # Skip if we can't draw at this position
    
    def draw_ui(self):
        """Draw UI elements (score, instructions)"""
        try:
            # Title
            title = "SNAKE GAME"
            if len(title) < self.width:
                self.stdscr.addstr(0, (self.width - len(title)) // 2, title, 
                                 curses.color_pair(3) if curses.has_colors() else curses.A_BOLD)
            
            # Score
            score_text = f"Score: {self.score}"
            if len(score_text) < self.width - 2:
                self.stdscr.addstr(0, 2, score_text, 
                                 curses.color_pair(3) if curses.has_colors() else curses.A_BOLD)
            
            # Instructions
            if self.height > self.start_row + self.game_height + 1:
                instructions = "Arrow keys: Move | Q: Quit | R: Restart (when game over)"
                if len(instructions) < self.width:
                    self.stdscr.addstr(self.height - 1, 0, instructions[:self.width-1])
            
            if self.game_over:
                # Game over message
                game_over_msg = f"GAME OVER! Final Score: {self.score} | Press 'R' to restart or 'Q' to quit"
                msg_row = self.start_row + self.game_height // 2
                msg_col = max(0, (self.width - len(game_over_msg)) // 2)
                
                if curses.has_colors():
                    self.stdscr.addstr(msg_row, msg_col, game_over_msg[:self.width-1], curses.color_pair(4))
                else:
                    self.stdscr.addstr(msg_row, msg_col, game_over_msg[:self.width-1], curses.A_REVERSE)
        except curses.error:
            pass  # Skip if we can't draw UI elements
    
    def handle_input(self):
        """Handle keyboard input"""
        key = self.stdscr.getch()
        
        if key == ord('q') or key == ord('Q'):
            return False  # Quit game
        
        if key == ord('r') or key == ord('R'):
            if self.game_over:
                self.reset_game()
            return True
        
        if self.game_over:
            return True
        
        # Handle direction changes
        if key == curses.KEY_UP and self.direction != Direction.DOWN:
            self.direction = Direction.UP
        elif key == curses.KEY_DOWN and self.direction != Direction.UP:
            self.direction = Direction.DOWN
        elif key == curses.KEY_LEFT and self.direction != Direction.RIGHT:
            self.direction = Direction.LEFT
        elif key == curses.KEY_RIGHT and self.direction != Direction.LEFT:
            self.direction = Direction.RIGHT
        
        return True
    
    def update_game(self):
        """Update game state"""
        if self.game_over:
            return
        
        # Get current head position
        head_row, head_col = self.snake[0]
        
        # Calculate new head position
        d_row, d_col = self.direction.value
        new_head = (head_row + d_row, head_col + d_col)
        
        # Check wall collision
        if (new_head[0] < 0 or new_head[0] >= self.game_height or 
            new_head[1] < 0 or new_head[1] >= self.game_width):
            self.game_over = True
            return
        
        # Check self collision
        if new_head in self.snake:
            self.game_over = True
            return
        
        # Move snake
        self.snake.appendleft(new_head)
        
        # Check food collision
        if new_head == self.food:
            self.score += 10
            self.place_food()
        else:
            # Remove tail if no food eaten
            self.snake.pop()
    
    def draw_screen(self):
        """Draw the entire game screen"""
        self.stdscr.clear()
        self.draw_border()
        self.draw_snake()
        self.draw_food()
        self.draw_ui()
        self.stdscr.refresh()
    
    def run(self):
        """Main game loop"""
        while True:
            self.draw_screen()
            
            if not self.handle_input():
                break
            
            self.update_game()

def main(stdscr):
    """Main function to run the game"""
    try:
        # Check if terminal is large enough
        height, width = stdscr.getmaxyx()
        if height < 10 or width < 40:
            stdscr.clear()
            stdscr.addstr(0, 0, "Terminal too small! Please resize to at least 40x10")
            stdscr.refresh()
            stdscr.getch()
            return
        
        game = SnakeGame(stdscr)
        game.run()
        
    except KeyboardInterrupt:
        pass  # Handle Ctrl+C gracefully

if __name__ == "__main__":
    curses.wrapper(main)
