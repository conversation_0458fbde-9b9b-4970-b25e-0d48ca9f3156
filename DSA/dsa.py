# Loops
def loop_example():
    for i in range(5):
        print(i)
# Arrays/Strings
def array_string_example():
    arr = [1, 2, 3, 4, 5]
    for i in arr:
        print(i)
    s = "hello"
    for char in s:
        print(char)
# Linked Lists
class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next
# Trees
def tree_example():
    class TreeNode:
        def __init__(self, val=0, left=None, right=None):
            self.val = val
            self.left = left
            self.right = right

    root = TreeNode(1)
    root.left = TreeNode(2)
    root.right = TreeNode(3)
    print(root.val, root.left.val, root.right.val)
# Breadth First Search
def bfs_example(graph, start):
    visited = set()
    queue = [start]
    
    while queue:
        vertex = queue.pop(0)
        if vertex not in visited:
            print(vertex)
            visited.add(vertex)
            queue.extend(neighbor for neighbor in graph[vertex] if neighbor not in visited)
# Depth First Search
def dfs_example(graph, start, visited=None):
    if visited is None:
        visited = set()
    visited.add(start)
    print(start)
    
    for neighbor in graph[start]:
        if neighbor not in visited:
            dfs_example(graph, neighbor, visited)
# Stacks
def stack_example():
    stack = []
    stack.append(1)
    stack.append(2)
    stack.append(3)
    while stack:
        print(stack.pop())
# Queues
def queue_example():
    from collections import deque
    queue = deque()
    queue.append(1)
    queue.append(2)
    queue.append(3)
    while queue:
        print(queue.popleft())
# Graphs
def graph_example():
    graph = {
        'A': ['B', 'C'],
        'B': ['A', 'D', 'E'],
        'C': ['A', 'F'],
        'D': ['B'],
        'E': ['B', 'F'],
        'F': ['C', 'E']
    }
    print("Graph:", graph)    
# Hash Tables
def hash_table_example():
    hash_table = {}
    hash_table['apple'] = 1
    hash_table['banana'] = 2
    hash_table['orange'] = 3
    print("Hash Table:", hash_table)
# Sorting
def sorting_example():
    arr = [5, 2, 9, 1, 5, 6]
    arr.sort()
    print(arr)
# Greedy
def greedy_example():
    coins = [1, 5, 10, 25]
    amount = 63
    count = 0
    for coin in reversed(coins):
        while amount >= coin:
            amount -= coin
            count += 1
    print("Coins used:", count)
# DP
def dp_example():
    def fib(n):
        if n <= 1:
            return n
        dp = [0] * (n + 1)
        dp[1] = 1
        for i in range(2, n + 1):
            dp[i] = dp[i - 1] + dp[i - 2]
        return dp[n]
    
    print("Fibonacci of 10:", fib(10))
